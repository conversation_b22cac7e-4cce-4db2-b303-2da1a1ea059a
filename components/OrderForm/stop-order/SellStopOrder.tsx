"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AppSlider } from "@/components";
import { useEffect, useMemo, useState } from "react";
import { InputAmount } from "../components/InputAmount";
import { EOrderType } from "..";
import { multipliedBN } from "@/utils/number";
import AppNumber from "@/components/AppNumber";
import BigNumber from "bignumber.js";
import { ErrorMessages } from "@/types/errors";
import rf from "@/services/RequestFactory";
import { EOrderSide } from "../OrderFormMobile";
import { useOrderToast } from "../hooks/useOrderMessage";
import { EOrderStatus, TOrderAction, EOrderStopCondition } from "@/types/order";
import { errorMsg } from "@/libs/toast";
import { usePairContext } from "@/app/trade/[symbol]/provider";
import useAccountBalance from "@/hooks/useAccountBalance";
import { isEmpty } from "lodash";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { isInvalidNumber } from "@/utils/helper";

type TStopOrderSell = {
  orderType: EOrderType;
  defaultPrice?: string;
  defaultAmount?: string;
};

export const SellStopOrder = ({
  orderType,
  defaultPrice,
  defaultAmount,
}: TStopOrderSell) => {
  const [stopPrice, setStopPrice] = useState<string>("");
  const [limitPrice, setLimitPrice] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [total, setTotal] = useState<string>("");
  const [maxSell, setMaxSell] = useState<string>("");
  const [sliderValue, setSliderValue] = useState<number>(0);
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const { pairSetting } = usePairContext();
  // For sell orders, we use baseAsset balance (e.g., BTC balance when selling BTC/USDT)
  const { coinBalance } = useAccountBalance({ coin: pairSetting?.baseAsset });
  const showOrderToast = useOrderToast();
  const { tradingPair } = useSelector((state: RootState) => state.tradingPair);

  // Calculate max sell amount based on available balance
  useEffect(() => {
    if (!coinBalance.available || !pairSetting) {
      setMaxSell("");
      return;
    }

    // For sell orders, max sell is simply the available base asset balance
    setMaxSell(coinBalance.available);
  }, [coinBalance.available, pairSetting]);

  // Validation logic
  useEffect(() => {
    let error = "";

    // Check if stop price is provided
    if (!stopPrice || isInvalidNumber(stopPrice)) {
      setErrorMessage("");
      return;
    }

    // Check if limit price is provided for STOP_LIMIT orders
    if (
      orderType === EOrderType.STOP_LIMIT &&
      (!limitPrice || isInvalidNumber(limitPrice))
    ) {
      setErrorMessage("");
      return;
    }

    // Check if amount is provided
    if (!amount || isInvalidNumber(amount)) {
      setErrorMessage("");
      return;
    }

    // Validate minimum order size (total value)
    if (
      pairSetting?.minOrderSize &&
      BigNumber(total || 0).isLessThan(pairSetting.minOrderSize)
    ) {
      error = `Minimum order size is ${pairSetting.minOrderSize}`;
    }

    // Validate minimum quantity
    if (
      pairSetting?.minQuantity &&
      BigNumber(amount).isLessThan(pairSetting.minQuantity)
    ) {
      error = `Minimum quantity is ${pairSetting.minQuantity}`;
    }

    // Validate maximum quantity
    if (
      pairSetting?.maxQuantity &&
      BigNumber(amount).isGreaterThan(pairSetting.maxQuantity)
    ) {
      error = `Maximum quantity is ${pairSetting.maxQuantity}`;
    }

    // Check if user has sufficient balance (for sell orders, check base asset)
    if (
      coinBalance.available &&
      BigNumber(amount || 0).isGreaterThan(coinBalance.available)
    ) {
      error = ErrorMessages.INVALID_BALANCE;
    }

    setErrorMessage(error);
  }, [
    stopPrice,
    limitPrice,
    amount,
    total,
    coinBalance.available,
    pairSetting,
    orderType,
  ]);

  // Handle orderbook selection - defaultPrice (for stop price or limit price)
  useEffect(() => {
    if (!defaultPrice) {
      return;
    }

    // For stop orders, we typically set the stop price from orderbook selection
    // But for STOP_LIMIT orders, we might want to set the limit price instead
    if (orderType === EOrderType.STOP_LIMIT) {
      // For STOP_LIMIT, set the limit price from orderbook selection
      setLimitPrice(defaultPrice);
      onChangeLimitPrice(defaultPrice);
    } else {
      // For STOP_MARKET, set the stop price from orderbook selection
      setStopPrice(defaultPrice);
      onChangeStopPrice(defaultPrice);
    }
  }, [defaultPrice, orderType]);

  // Handle orderbook selection - defaultAmount
  useEffect(() => {
    if (
      !defaultAmount ||
      !coinBalance.available ||
      BigNumber(coinBalance.available).isZero()
    ) {
      return;
    }

    // Ensure the amount doesn't exceed available balance
    const correctAmount = BigNumber(coinBalance.available).isGreaterThan(
      defaultAmount
    )
      ? defaultAmount
      : coinBalance.available;

    setAmount(correctAmount);
    onChangeAmount(correctAmount);
  }, [defaultAmount, coinBalance.available]);

  const onChangeStopPrice = (value: string) => {
    if (isInvalidNumber(value)) {
      setStopPrice("");
      return;
    }
    setStopPrice(value);
  };

  const onChangeLimitPrice = (value: string) => {
    if (isInvalidNumber(value)) {
      setLimitPrice("");
      setTotal("");
      setAmount("");
      setSliderValue(0);
      return;
    }

    setLimitPrice(value);

    // Recalculate total if amount exists
    if (amount && !isInvalidNumber(amount)) {
      const totalValue = multipliedBN(amount, value);
      setTotal(
        BigNumber(totalValue).toFixed(
          pairSetting?.pricePrecision || 0,
          BigNumber.ROUND_DOWN
        )
      );
    }
  };

  const onChangeAmount = (value: string) => {
    if (isInvalidNumber(value)) {
      setAmount("");
      setTotal("");
      setSliderValue(0);
      return;
    }

    setAmount(value);

    // Use limit price for STOP_LIMIT, market price for STOP_MARKET
    const priceToUse =
      orderType === EOrderType.STOP_LIMIT ? limitPrice : tradingPair?.lastPrice;

    if (priceToUse && !isInvalidNumber(priceToUse)) {
      const totalValue = multipliedBN(value, priceToUse);
      setTotal(
        BigNumber(totalValue).toFixed(
          pairSetting?.pricePrecision || 0,
          BigNumber.ROUND_DOWN
        )
      );
    }

    updateSliderValue(value);
  };

  const onChangeSlider = (value: number | string) => {
    const percentage = Number(value);
    setSliderValue(percentage);

    if (
      !coinBalance.available ||
      BigNumber(coinBalance.available).isLessThanOrEqualTo(0)
    ) {
      return;
    }

    // For sell orders, calculate amount based on percentage of base asset balance
    const amountValue = BigNumber(coinBalance.available)
      .multipliedBy(percentage)
      .dividedBy(100)
      .toFixed(pairSetting?.quantityPrecision || 0, BigNumber.ROUND_DOWN);

    setAmount(amountValue);

    // Calculate total based on price
    const priceToUse =
      orderType === EOrderType.STOP_LIMIT ? limitPrice : tradingPair?.lastPrice;

    if (priceToUse && !isInvalidNumber(priceToUse)) {
      const totalValue = multipliedBN(amountValue, priceToUse);
      setTotal(
        BigNumber(totalValue).toFixed(
          pairSetting?.pricePrecision || 0,
          BigNumber.ROUND_DOWN
        )
      );
    }
  };

  const onChangeTotal = (value: string) => {
    if (isInvalidNumber(value)) {
      setTotal("");
      setAmount("");
      setSliderValue(0);
      return;
    }

    setTotal(value);

    // Calculate amount based on price
    const priceToUse =
      orderType === EOrderType.STOP_LIMIT ? limitPrice : tradingPair?.lastPrice;

    if (priceToUse && !isInvalidNumber(priceToUse)) {
      const amountValue = BigNumber(value)
        .div(priceToUse)
        .toFixed(pairSetting?.quantityPrecision || 0, BigNumber.ROUND_DOWN);
      setAmount(amountValue);
      updateSliderValue(amountValue);
    }
  };

  const updateSliderValue = (currentAmount: string) => {
    if (
      !coinBalance.available ||
      BigNumber(coinBalance.available).isLessThanOrEqualTo(0)
    ) {
      setSliderValue(0);
      return;
    }
    const newSliderValue = BigNumber(currentAmount)
      .div(coinBalance.available)
      .multipliedBy(100)
      .toFixed();

    setSliderValue(parseInt(newSliderValue));
  };

  const handlePlaceOrder = async () => {
    if (isEmpty(pairSetting)) {
      return;
    }

    setIsLoading(true);
    try {
      // Get current market price for comparison
      const currentPrice = tradingPair?.lastPrice;
      if (!currentPrice || isInvalidNumber(currentPrice)) {
        errorMsg("Unable to get current market price");
        return;
      }

      // Determine stop condition based on stop price vs current price
      const stopCondition = BigNumber(stopPrice).isGreaterThan(currentPrice)
        ? EOrderStopCondition.GREATER_THAN
        : EOrderStopCondition.LESS_THAN;

      const orderParams = {
        symbol: pairSetting.symbol,
        side: EOrderSide.SELL,
        type: orderType,
        quantity: BigNumber(amount).toFixed(
          pairSetting?.quantityPrecision || 0
        ),
        stop_price: stopPrice,
        stop_condition: stopCondition,
      } as any;

      // Add limit price for STOP_LIMIT orders
      if (orderType === EOrderType.STOP_LIMIT) {
        orderParams.price = limitPrice;
      }

      const res = await rf.getRequest("OrderRequest").placeOrder(orderParams);

      showOrderToast({
        baseAsset: pairSetting.baseAsset,
        quoteAsset: pairSetting.quoteAsset,
        action: TOrderAction.PLACE,
        quantity: res.orig_qty,
        status: EOrderStatus.NEW,
        type: orderType,
        side: EOrderSide.SELL,
      });

      // Reset form
      setStopPrice("");
      setLimitPrice("");
      setTotal("");
      setAmount("");
      setSliderValue(0);
    } catch (error: any) {
      errorMsg(`Error placing order ${error?.message || ""}`);
      console.log(error, "handlePlaceOrder error");
    } finally {
      setIsLoading(false);
    }
  };

  // Button validation logic
  const isDisableSellButton = useMemo(() => {
    if (errorMessage.length > 0) {
      return true;
    }

    if (!stopPrice || isInvalidNumber(stopPrice)) {
      return true;
    }

    if (
      orderType === EOrderType.STOP_LIMIT &&
      (!limitPrice || isInvalidNumber(limitPrice))
    ) {
      return true;
    }

    if (!amount || isInvalidNumber(amount)) {
      return true;
    }

    return isLoading;
  }, [errorMessage, stopPrice, limitPrice, amount, isLoading, orderType]);

  return (
    <div className="flex flex-col gap-4">
      <InputAmount
        value={stopPrice}
        label={"Stop"}
        onChange={onChangeStopPrice}
        prefix={pairSetting?.quoteAsset?.toUpperCase()}
        decimal={pairSetting?.pricePrecision}
        isHasArrowChange
      />

      {orderType === EOrderType.STOP_LIMIT ? (
        <>
          <InputAmount
            value={limitPrice}
            label={"Limit"}
            onChange={onChangeLimitPrice}
            prefix={pairSetting?.quoteAsset?.toUpperCase()}
            decimal={pairSetting?.pricePrecision}
            isHasArrowChange
          />
        </>
      ) : (
        <div className="bg-white-50 flex rounded-[6px]">
          <div className="flex w-full cursor-not-allowed justify-between px-3 py-2">
            <div className="body-md-regular-14 text-white-500">Price</div>
            <div className="body-md-regular-14 text-white-500">
              Market price
            </div>
          </div>
        </div>
      )}

      <InputAmount
        value={amount}
        label={"Amount"}
        onChange={onChangeAmount}
        prefix={pairSetting?.baseAsset?.toUpperCase()}
        decimal={pairSetting?.quantityPrecision}
        isHasArrowChange
      />

      <AppSlider
        handleChange={onChangeSlider}
        value={sliderValue}
        setValue={setSliderValue}
      />

      {orderType === EOrderType.STOP_LIMIT && (
        <InputAmount
          value={total}
          label={"Total"}
          onChange={onChangeTotal}
          placeholder="Minimum 5"
          prefix={pairSetting?.quoteAsset?.toUpperCase()}
        />
      )}

      {errorMessage && (
        <div className="body-sm-regular-12 text-red-500">{errorMessage}</div>
      )}

      <div className="flex flex-col gap-2">
        <div className="flex justify-between">
          <div className="body-sm-regular-12 text-white-500">Avbl</div>
          <div className="body-sm-medium-12 flex items-center gap-1">
            <AppNumber
              value={coinBalance.available}
              decimals={pairSetting?.quantityPrecision}
              isFormatLargeNumber={false}
            />{" "}
            {pairSetting?.quoteAsset?.toUpperCase()}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="body-sm-regular-12 text-white-500">Max Sell</div>
          <div className="body-sm-medium-12 flex items-center gap-1">
            <AppNumber
              value={maxSell}
              decimals={pairSetting?.quantityPrecision}
              isFormatLargeNumber={false}
            />
            {pairSetting?.baseAsset?.toUpperCase()}
          </div>
        </div>
      </div>

      <AppButton
        variant="sell"
        size="large"
        onClick={handlePlaceOrder}
        isLoading={isLoading}
        disabled={isDisableSellButton}
      >
        Sell
      </AppButton>
    </div>
  );
};
