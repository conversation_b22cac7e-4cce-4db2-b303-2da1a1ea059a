import { EOrderType } from "../index";
import { BuyOrderForm } from "../simple-order/BuyOrderForm";
import { BuyStopOrder } from "../stop-order/BuyStopOrder";

export const BuyOrder = ({
  orderType,
  selectedPrice,
  selectedBuyAmount,
}: {
  orderType: EOrderType;
  selectedPrice?: string;
  selectedBuyAmount?: string;
}) => {
  if (orderType === EOrderType.LIMIT || orderType === EOrderType.MARKET) {
    return (
      <BuyOrderForm
        orderType={orderType}
        defaultPrice={selectedPrice}
        defaultAmount={selectedBuyAmount}
      />
    );
  }
  return (
    <BuyStopOrder
      orderType={orderType}
      defaultPrice={selectedPrice}
      defaultAmount={selectedBuyAmount}
    />
  );
};
