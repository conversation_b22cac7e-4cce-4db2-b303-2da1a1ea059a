import { EOrderType } from "../index";
import { SellOrderForm } from "../simple-order/SellOrderForm";
import { SellStopOrder } from "../stop-order/SellStopOrder";

export const SellOrder = ({
  orderType,
  selectedPrice,
  selectedSellAmount,
}: {
  orderType: EOrderType;
  selectedPrice?: string;
  selectedSellAmount?: string;
}) => {
  if (orderType === EOrderType.LIMIT || orderType === EOrderType.MARKET) {
    return (
      <SellOrderForm
        orderType={orderType}
        defaultPrice={selectedPrice}
        defaultAmount={selectedSellAmount}
      />
    );
  }
  return (
    <SellStopOrder
      orderType={orderType}
      defaultPrice={selectedPrice}
      defaultAmount={selectedSellAmount}
    />
  );
};
